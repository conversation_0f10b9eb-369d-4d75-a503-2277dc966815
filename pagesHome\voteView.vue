<template>
	<view class="vote-view">
		<Navbar title="第三届月湖杯3D大赛校赛网络投票" :leftIconColor="'#3b65a5'" />
		<view class="vote-swiper">
			<u-swiper :list="bannerList" height="376rpx" radius="0" :autoplay="false"></u-swiper>
		</view>
		<view v-if="voteData.activityDesc">
			<view class="vote-content">
				<u-parse :content="voteData.activityDesc || ''"></u-parse>
			</view>
			<view class="flex-box-center">
				<image :src="imageUrl + '/vote-down-icon.png'" class="vote-down-icon"></image>
				<!-- <image src="../static/dotu.gif" mode="" class="vote-down-icon"></image> -->
			</view>
		</view>
		<view class="vote-situation-box flex-box-space-between">
			<view class="vote-situation-left">
				当前票数：{{voteData.totalVoteNum || 0}}票
			</view>
			<view class="vote-situation-right flex-box-space-between" @click="goToUrl('voteSituation', {tabsIndex})">
				查看投票情况
				<image :src="imageUrl + '/vote-left-icon.png'"></image>
			</view>
		</view>
		<view class="situation-nav">
			<u-tabs :list="navList" itemStyle="height: 44px; flex: 1;" lineWidth="100rpx" lineColor="#3A64A5"
				activeStyle="color: #3A64A5; font-size:32rpx;font-weight: bold;" inactiveStyle="font-size: 32rpx; color: #333333;"
				@click="onTabs"></u-tabs>
		</view>
		<view class="situation-list">
			<view class="situation-item" v-for="(item, index) in dataList" :key="index">
				<view class="situation-title">
					{{item.workName || ''}}
				</view>
				<view class="situation-group flex-box-space-between">
					参赛队伍：{{item.partTeam || ''}}
					<view class="group-vote" @click="voteSubmit(item.workId)">
						为TA投一票
					</view>
				</view>
				<view class="situation-content">
					<view v-if="item.imgs.length">
						<scroll-view :scroll-x="true" class="situation-image-box flex-box-center-y" :enhanced="true"
							:show-scrollbar="true">
							<image :src="imgItem" v-for="(imgItem, imgIndex) in item.imgs" :key="imgIndex" mode="widthFix"></image>
						</scroll-view>
						<view class="tips-image" v-if="item.imgs.length > 1">
							左右滑动图片可查看更多
						</view>
					</view>
					<!-- <scroll-view :scroll-x="true" class="situation-gif-box" :enhanced="true" :show-scrollbar="true">
						<image :src="imgItem" v-for="(imgItem, imgIndex) in bannerList" :key="imgIndex"></image>
					</scroll-view> -->
					<view v-if="item.logoVideoUrl" :scroll-x="true" class="situation-video-box" :enhanced="true"
						:show-scrollbar="true">
						<video :src="item.logoVideoUrl"></video>
					</view>
				</view>
				<view class="situation-desc">
					<text style="color: #333333; font-weight: bold;">作品简介：</text>
					<u-parse :content="item.workDesc"></u-parse>
				</view>
			</view>
			<!-- 没有内容展示 -->
			<view class="situation-available" v-if="total == 0">
				<image :src="imageUrl + '/currentlyUnavailable.png'" mode="widthFix" style="width:265rpx;height:301rpx;margin-top:158rpx;"></image>
			</view>
		</view>
		<view class="tips-box">
			<view>本次大赛评分满分为100分</view>
			<view>网络投票评分占30%、初评分占70%的方式</view>
			评选出本次大赛的一、二、三等奖。
		</view>
	</view>
</template>

<script>
	import {
		voteIndex,
		getWorkList,
		voteSubmit
	} from "@/api/vote/vote.js"
	import {
		wxLogin
	} from "@/api/public/index.js"
	export default {
		data() {
			return {
				bannerList: [ 
					// 'https://xnlv.lzxx8848.com/image/xnlv/static/shear-image.png'
					'https://xnlv.lizxx.com/image/xnlv/static/braanTU.png'
				],
				imageUrl: this.$imageUrl,
				navList: [{
					name: '数字工业',
					type: 'szgy'
				}, {
					name: '数字人居',
					type: 'szrj'
				}, {
					name: '数字文化',
					type: 'szwh'
				}, {
					name: '元宇宙',
					type: 'yyz'
				}],
				dataList: [],
				pageNum: 1,
				type: 'szgy',
				total: -1,
				voteData: {},
				tabsIndex: 0,
				show: true,
				voteId: ''
			}
		},
		onShareAppMessage() {
			return {
				title: '2024长沙学院第三届‘月湖杯’网络投票开始啦~',
				path: '/pages/home?isVote=vote',
				imageUrl: 'https://xnlv.lzxx8848.com/image/xnlv/static/shear-image.png'
			}
		},
		onShareTimeline() {
			return {
				title: '2024长沙学院第三届‘月湖杯’网络投票开始啦~',
			};
		},
		onLoad() {
			this.getWorkList()
		},
		onShow() {
			this.voteIndex()
		},
		onReachBottom() {
			if (this.dataList.length >= this.total) return
			this.pageNum++
			this.getWorkList()
		},
		methods: {
			async getWorkList() {
				let res = await getWorkList({
					workClassify: this.type,
					pageSize: 6,
					pageNum: this.pageNum
				})
				this.dataList.push(...res.rows)
				this.total = res.total
			},
			async voteIndex() {
				let res = await voteIndex()
				this.voteData = res.data || {}
			},
			async voteSubmit(id) {
				this.voteId = id
				if (wx.getLaunchOptionsSync().scene === 1154) {
					uni.showToast({
						title: '请前往小程序使用完整服务',
						icon: 'none',
						duration: 3000
					})
					return
				}
				if (!uni.getStorageSync('token')?.length) {
					this.getCode()
					return
				}
				let res = await voteSubmit(this.voteId)
				if (res?.code == 200) {
					uni.showToast({
						title: "投票成功",
						icon: "success",
						duration: 3000,
						mask: true
					})
					this.voteIndex()
				}
			},
			onTabs(e) {
				if (this.tabsIndex == e.index) return
				this.tabsIndex = e.index
				this.type = e.type
				this.pageNum = 1
				this.total = -1
				this.dataList = []
				this.getWorkList()
			},
			// 获取code登录
			getCode() {
				uni.login({
					provider: 'weixin',
					success: res => {
						this.code = res.code
						this.wxLogin()
					},
					fail: err => {
						console.log(err)
					}
				})
			},
			// 登录请求
			async wxLogin() {
				let res = await wxLogin(this.code, this.$appID)
				uni.setStorageSync('token', res.data.token);
				uni.setStorageSync('wxUser', res.data.wxUser);
				uni.setStorageSync('openId', res.data.openId);
				this.voteSubmit(this.voteId)
			},
			goToUrl(urlName, params = {}) {
				this.$Router.push({
					name: urlName,
					params
				})
			}
		}
	}
</script>

<style lang="scss">
	::v-deep .u-tabs__wrapper__nav__item {
		flex: 1 !important;
		padding: 0 !important;
	}

	.vote-view {
		background-color: #e8f1fe;
		min-height: 100vh;
	}

	.u-content {
		padding: 24rpx;
	}

	.vote-down-icon {
		width:60rpx;
		height:60rpx;
		margin-top: 24rpx;
		animation: zoom 1.5s infinite
		// transform: rotate(90deg);
	}
	@keyframes zoom {
	from {
	    transform: translateY(-50%);
	  }
	  to {
	    transform: translateY(0);
	  }
	}

	.vote-situation-box {
		padding: 20rpx;
		background-color: #fff;
		margin: 20rpx 30rpx 10rpx;
		border-radius: 16rpx;

		.vote-situation-left {
			font-size: 30rpx;
			color: #333333;
		}

		.vote-situation-right {
			font-size: 30rpx;
			color: #3A64A5;

			image {
				width: 30rpx;
				height: 30rpx;
				margin-left: 12rpx;
			}
		}
	}

	.situation-nav {
		padding-bottom: 20rpx;
	}

	.situation-list {
		padding: 18rpx;
		min-height: 80vh;
	}

	.situation-item {
		padding: 42rpx 32rpx 32rpx;
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 16rpx;

		.situation-title {
			font-size:32rpx;
			color: #333333;
			padding-bottom: 34rpx;
			border-bottom: 2rpx solid #EFEFEF;
			font-weight: bold
		}

		.situation-group {
			font-size: 24rpx;
			color: #666666;
			margin-top: 16rpx;
		}

		.group-vote {
			width: 190rpx;
			height: 68rpx;
			background: #E8F1FE;
			border-radius: 4rpx;
			text-align: center;
			line-height: 64rpx;
			font-size: 28	rpx;
			color: #3A64A5;
			font-weight:bold;
		}

		.situation-content {
			margin-top: 16rpx;

			.situation-image-box {
				width: 100%;
				white-space: nowrap;
				border-radius: 8rpx;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.situation-gif-box {
				width: 100%;
				height: 362rpx;
				white-space: nowrap;
				margin-top: 14rpx;
				border-radius: 8rpx;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			.situation-video-box {
				width: 100%;
				height: 362rpx;
				margin-top: 14rpx;
				border-radius: 8rpx;
				overflow: hidden;

				video {
					width: 100%;
					height: 362rpx;
					border-radius: 8rpx;
				}
			}
		}

		.situation-desc {
			margin-top: 22rpx;
			font-size: 28rpx;
			color: #666666;
			line-height:2em;
		}
	}

	.tips-box {
		font-size:28rpx;
		color: #333333;
		margin-top: 42rpx;
		padding-bottom: 200rpx;
		text-align: center;
		line-height:2em;
	}

	.vote-content {
		font-size: 28rpx !important;
		padding: 0 28rpx;
	}

	.tips-image {
		font-size: 28rpx;
		color: #333333;
		text-align: center;
		margin-top: 4rpx;
	}
	.situation-available{
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>