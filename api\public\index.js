import Http from '@/api/Http.js'
import Api from "@/api/index.js"

export function wxLogin(code, appid) {
	return Http.request({
		url: '/wx/user/login?jsCode=' + code + '&appid=' + appid,
		method: 'POST',
	})
}
// 获取省份
export function getProvinceList() {
	return Http.request({
		url: '/getProvinceList',
		method: 'GET',
	})
}

// 解析用户手机号
export function decryptPhone(data) {
	return Http.request({
		url: '/wx/user/decryptPhone',
		method: 'POST',
		data
	})
}
// 生成小程序太阳码
export function generateQrcode(data) {
	return Api.postDataHttp('/wx/user/generateQrcode', data)
}

// 获取城市 
export function getCityList(id) {
	return Http.request({
		url: '/getCityList/' + id,
		method: 'GET',
	})
}
// 获取区县
export function getDistrictList(id) {
	return Http.request({
		url: '/getDistrictList/' + id,
		method: 'GET',
	})
}