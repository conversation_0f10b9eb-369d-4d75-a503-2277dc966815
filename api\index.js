import Http from '@/api/Http.js'

var Api = {
	// 根据data查询列表
	getDataHttp: (url, data) => {
		return Http.request({
			url: url,
			method: 'GET',
			data
		})
	},
	// 根据ID查询列表
	getIdHttp: (url, id) => {
		return Http.request({
			url: url + id,
			method: 'GET'
		})
	},
	postDataHttp: (url, data) => {
		return Http.request({
			url: url,
			method: 'POST',
			data
		})
	},
	postIdHttp: (url, id) => {
		return Http.request({
			url: url + id,
			method: 'POST'
		})
	},
	putDataHttp: (url, data) => {
		return Http.request({
			url: url,
			method: 'PUT',
			data
		})
	},
	// 根据id修改
	putIdHttp: (url, id) => {
		return Http.request({
			url: url + id,
			method: 'PUT'
		})
	},
	// 根据id删除
	deleteIdHttp: (url, id) => {
		return Http.request({
			url: url + id,
			method: 'DELETE'
		})
	}
}
export default Api