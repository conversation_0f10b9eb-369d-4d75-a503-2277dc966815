(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesHome/personalizedMap"],{1679:function(t,i,e){"use strict";var n=e("3437"),a=e.n(n);a.a},3437:function(t,i,e){},"532d":function(t,i,e){"use strict";e.r(i);var n=e("6c28"),a=e("e5ec");for(var r in a)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(r);e("ce21"),e("1679");var u=e("828b"),l=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);i["default"]=l.exports},"6c28":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]},8635:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=e("cf81"),a={data:function(){return{imageUrl:this.$imageUrl,listArr:[],covers:[{id:0,width:85,height:119,latitude:"28.480521",longitude:"112.758725",url:"https://xnlv.lzxx8848.com/image/xnlv/static/vrTravel/qiaKou.png"},{id:1,width:84,height:161,latitude:"28.45748",longitude:"112.79028",url:"https://xnlv.lzxx8848.com/image/xnlv/static/vrTravel/Jinggang.png"},{id:2,width:86,height:118,latitude:"28.405705",longitude:"112.80777",url:"https://xnlv.lzxx8848.com/image/xnlv/static/vrTravel/Xinkang.png"},{id:3,width:80,height:161,latitude:"28.435701",longitude:"112.824036",url:"https://xnlv.lzxx8848.com/image/xnlv/static/vrTravel/copperOfficials.png"},{id:4,width:82,height:158,latitude:"28.393006",longitude:"112.852475",url:"https://xnlv.lzxx8848.com/image/xnlv/static/vrTravel/shutangMountain.png"}],id:0,title:"map",iconPath:"",width:0,height:0}},onLoad:function(){this.qqmapsdk=new n({key:"N3ZBZ-FI73T-PUXXN-V6JUL-UKQS5-HLBTR"}),this.getList()},methods:{getList:function(){var t=this,i=[];this.covers.map((function(e){var n={id:e.id,width:e.width,height:e.height,latitude:t.latitude=e.latitude,longitude:t.longitude=e.longitude,iconPath:e.url};i.push(n)})),this.covers=i},onMarkerTap:function(t){var i=this.covers.find((function(i){return i.id===t.markerId})),e=i.id;this.$Router.push({name:"vrBrowsing",params:{orderId:e}})}}};i.default=a},a35e:function(t,i,e){"use strict";(function(t,i){var n=e("47a9");e("5a94");n(e("3240"));var a=n(e("532d"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},cba1:function(t,i,e){},ce21:function(t,i,e){"use strict";var n=e("cba1"),a=e.n(n);a.a},e5ec:function(t,i,e){"use strict";e.r(i);var n=e("8635"),a=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(r);i["default"]=a.a}},[["a35e","common/runtime","common/vendor"]]]);