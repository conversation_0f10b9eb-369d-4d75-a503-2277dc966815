{"name": "luch-request", "version": "3.1.1", "description": "基于Promise实现uni-app request 请求插件", "keywords": ["uni-app", "request", "Promise", "luch", "luch-request"], "main": "src/lib/luch-request.js", "scripts": {"dev": "rollup -c", "watch": "rollup -c -w", "build": "rimraf DCloud && rollup -c --environment NODE_ENV:production", "zipD": "node node/zipDCloudPlugin.js && node node/zipDCloudDemo.js", "docs": "vuepress dev docs", "docs:build": "vuepress build docs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -w -r 0", "pub": "npm publish && cnpm sync luch-request", "pub:alpha": "npm publish --tag=alpha && cnpm sync luch-request", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/lei-mu/luch-request.git"}, "author": "luch", "license": "MIT", "bugs": {"url": "https://github.com/lei-mu/luch-request/issues"}, "homepage": "https://www.quanzhan.co/luch-request/", "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.8.3", "@babel/preset-env": "^7.9.5", "@vuepress/plugin-active-header-links": "^1.5.0", "@vuepress/plugin-pwa": "^1.5.2", "archiver": "^4.0.1", "babel-eslint": "^10.1.0", "babel-preset-es2015": "^6.24.1", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.1.1", "cz-conventional-changelog": "^3.2.0", "eslint": "^6.8.0", "grunt": "^1.1.0", "grunt-babel": "^8.0.0", "load-grunt-tasks": "^5.1.0", "node-zip": "^1.1.1", "rollup": "^2.66.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-copy": "^3.3.0", "rollup-plugin-eslint": "^7.0.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-live-server": "^1.0.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.4", "rollup-plugin-zip": "^1.0.0", "validate-commit-msg": "^2.14.0", "vuepress": "^1.4.1"}, "engines": {}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "ghooks": {"commit-msg": "validate-commit-msg"}, "validate-commit-msg": {"types": ["feat", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "revert"], "scope": {"required": false, "allowed": ["*"], "validate": false, "multiple": false}, "warnOnFail": false, "maxSubjectLength": 100, "subjectPattern": ".+", "subjectPatternErrorMsg": "subject does not match subject pattern!", "helpMessage": "", "autoFix": false}}, "dependencies": {"@dcloudio/types": "^2.0.16"}}