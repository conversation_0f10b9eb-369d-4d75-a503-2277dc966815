<view class="vote-view"><navbar vue-id="4111e31d-1" title="第三届月湖杯3D大赛校赛网络投票" leftIconColor="#3b65a5" bind:__l="__l"></navbar><view class="vote-swiper"><u-swiper vue-id="4111e31d-2" list="{{bannerList}}" height="376rpx" radius="0" autoplay="{{false}}" bind:__l="__l"></u-swiper></view><block wx:if="{{voteData.activityDesc}}"><view><view class="vote-content"><u-parse vue-id="4111e31d-3" content="{{voteData.activityDesc||''}}" bind:__l="__l"></u-parse></view><view class="flex-box-center"><image class="vote-down-icon" src="{{imageUrl+'/vote-down-icon.png'}}"></image></view></view></block><view class="vote-situation-box flex-box-space-between"><view class="vote-situation-left">{{'当前票数：'+(voteData.totalVoteNum||0)+'票'}}</view><view data-event-opts="{{[['tap',[['goToUrl',['voteSituation',['o',['tabsIndex',tabsIndex]]]]]]]}}" class="vote-situation-right flex-box-space-between" bindtap="__e">查看投票情况<image src="{{imageUrl+'/vote-left-icon.png'}}"></image></view></view><view class="situation-nav"><u-tabs vue-id="4111e31d-4" list="{{navList}}" itemStyle="height: 44px; flex: 1;" lineWidth="100rpx" lineColor="#3A64A5" activeStyle="color: #3A64A5; font-size: 30rpx;" inactiveStyle="font-size: 30rpx; color: #333333;" data-event-opts="{{[['^click',[['onTabs']]]]}}" bind:click="__e" bind:__l="__l"></u-tabs></view><view class="situation-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="situation-item"><view class="situation-title">{{''+(item.$orig.workName||'')+''}}</view><view class="situation-group flex-box-space-between">{{'参赛队伍：'+(item.$orig.partTeam||'')+''}}<view data-event-opts="{{[['tap',[['voteSubmit',['$0'],[[['dataList','',index,'workId']]]]]]]}}" class="group-vote" bindtap="__e">为TA投一票</view></view><view class="situation-content"><block wx:if="{{item.g0}}"><view><scroll-view class="situation-image-box flex-box-center-y" scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{true}}"><block wx:for="{{item.$orig.imgs}}" wx:for-item="imgItem" wx:for-index="imgIndex" wx:key="imgIndex"><image src="{{imgItem}}" mode="widthFix"></image></block></scroll-view><block wx:if="{{item.g1>1}}"><view class="tips-image">左右滑动图片可查看更多</view></block></view></block><block wx:if="{{item.$orig.logoVideoUrl}}"><view class="situation-video-box" scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{true}}"><video src="{{item.$orig.logoVideoUrl}}"></video></view></block></view><view class="situation-desc"><text style="color:#333333;font-weight:bold;">作品简介：</text><u-parse vue-id="{{'4111e31d-5-'+index}}" content="{{item.$orig.workDesc}}" bind:__l="__l"></u-parse></view></view></block></view><view class="tips-box"><view>本次大赛评分满分为100分</view><view>网络投票评分占30%、初评分占70%的方式</view>评选出本次大赛的一、二、三等奖。</view></view>