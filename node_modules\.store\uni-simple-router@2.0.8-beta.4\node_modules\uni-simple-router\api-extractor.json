// this the shared base config for all packages.
{
    "$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json",
  
    "mainEntryPointFilePath": "./dist/src/index.d.ts",
  
    "apiReport": {
      "enabled": true,
      "reportFolder": "./temp/"
    },
  
    "docModel": {
      "enabled": true
    },
  
    "dtsRollup": {
      "enabled": true,
      "untrimmedFilePath": "./dist/<unscopedPackageName>.d.ts"
    },
  
    "tsdocMetadata": {
      "enabled": false
    },
  
    "messages": {
      "compilerMessageReporting": {
        "default": {
          "logLevel": "warning"
        }
      },
  
      "extractorMessageReporting": {
        "default": {
          "logLevel": "warning",
          "addToApiReportFile": true
        },
  
        "ae-missing-release-tag": {
          "logLevel": "none"
        }
      },
  
      "tsdocMessageReporting": {
        "default": {
          "logLevel": "warning"
        }
      }
    }
  }
  