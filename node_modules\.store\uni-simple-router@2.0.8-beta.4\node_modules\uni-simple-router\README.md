# uni-simple-router

> 一个更为简洁的[Vue-router](https://router.vuejs.org/zh/)，专为 [uni-app](https://uniapp.dcloud.io/) 量身打造

## 介绍

`uni-simple-router` 是专为 [uni-app](https://uniapp.dcloud.io/) 打造的路由器。它与 [uni-app](https://uniapp.dcloud.io/) 核心深度集成，使使用 [uni-app](https://uniapp.dcloud.io/) 轻松构建单页应用程序变得轻而易举。功能包括：

* `H5端` 能完全使用 `vue-router` 进行开发。

* 模块化，基于组件的路由器配置。

* 路由参数，查询，通配符。

* `H5端` 查看由 `uni-simple-router` 过渡系统提供动力的过渡效果。

* 更细粒度的导航控制。

* `H端`自动控制活动的CSS类链接。

* 通配小程序端、APP端、H5端。


开始使用 [查看文档](http://hhyang.cn)，或 [使用示例](https://github.com/SilurianYang/uni-simple-router/tree/master/examples)（请参见下面的示例）。

## 问题
在提交问题的之前，请确保阅读 [“问题报告清单”](https://github.com/SilurianYang/uni-simple-router/issues/new?assignees=&labels=&template=bug_report.md&title=) 。不符合准则的问题可能会立即被解决。

## 贡献
提出拉取请求之前，请务必先阅读 [查看文档](http://hhyang.cn)（请参见下面的示例）。。

## 变更日志
[发行说明](https://github.com/SilurianYang/uni-simple-router/releases) 中记录了每个发行版的详细信息更改。

## 特别感谢

特别感谢 [markrgba](https://github.com/markrgba) 一直以来对文档和相关测试的维护。

## 技术交流

<a target="_blank" href="//shang.qq.com/wpa/qunwpa?idkey=0f4d7f38e5d15dd49bf7c3032c80ed3f54ecfa3dd800053d6ae145c869f9eb47"><img border="0" src="http://pub.idqqimg.com/wpa/images/group.png" alt="uni-app  插件" title="uni-app  插件"></a>


## 成品预览

<div style="display: -webkit-box;display: flex; flex-direction: column;align-items: center;">
    <p style="color: #3eaf7c;font-size:18px">uni-simple-router@2.0+ts+uni-app</p>
    <img src="https://hhyang.cn/images/ad1.jpg" width="200" height="200">
</div>