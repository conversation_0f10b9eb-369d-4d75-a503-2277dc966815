<template>
	<view class="paers-max" >
		<!-- 头部导航栏 -->
	<view class="paers-Navbar">
		<Navbar  :leftIconColor="'#0000000'" :bgColor="'#0000000'"  />
	</view>
		<!-- 背景 :style="{backgroundImage: 'url('+imageUrl + '/vrTravel/blueSky.png'+')'}"-->
	<view class="paers-background">
		<image :src="imageUrl + '/vrTravel/blueSky.png'" mode="widthFix" style="width:100%;height:100%;"></image>
	</view>
		<!--地图 -->
	<view>
	<!-- 112.79028  28.45748-->
		<map style="width:100%;height:100vh;" id="map" subkey="OQ6BZ-ICEE7-KQJXO-HWM7C-2NCRH-SIF2T"
			longitude="112.821728" latitude="28.467443" scale="11" layer-style="1" :markers="covers" @markertap="onMarkerTap"></map>
	</view>
</view>
</template>
<script>
	const QQMapWX = require('../utils/qqmap-wx-jssdk/qqmap-wx-jssdk.js');
	export default {
		data() {
			return {
				imageUrl: this.$imageUrl,
				listArr: [],
				covers: [{
					// 乔口古镇
						id: 0,
						width:85,
						height:119,
						// latitude: '28.480521',
						// longitude: '112.758725',
						latitude: '28.505255',
						longitude: '112.73176',
						url: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/qiaKou.png',
					},
					{
						// 靖港
						id: 1,
						width:84,
						height:161,
						latitude: '28.45748',
						longitude: '112.79028',
						url: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/Jinggang.png',
					},
					{   
						// 新康
						id: 2,
						width:86,
						height:118,
						// latitude: '28.405705',
						// longitude: '112.80777',
						latitude: '28.403888',
						longitude: '112.809337',
						url: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/Xinkang.png',
					},
					{
						// 铜官
						id: 3,
						width:80,
						height:161,
						latitude: '28.420027',
						longitude: '112.832902',
						// latitude: '28.435701',
						// longitude: '112.824036',
						url: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/copperOfficials.png',
					},
					{
						// 书堂山
						id: 4,
						width:82,
						height:158,
						// latitude: '28.393006',
						// longitude: '112.852475',
						latitude: '28.396778',
						longitude: '112.881487',
						url: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/shutangMountain.png',
					}
				],
				id: 0, // 使用 marker点击事件 需要填写id
				title: 'map',
				iconPath: '', //标点的图标
				width: 0, //宽高必填  不填报错
				height: 0,
			};
		},
		onLoad() {
			this.qqmapsdk = new QQMapWX({
				key: 'N3ZBZ-FI73T-PUXXN-V6JUL-UKQS5-HLBTR'
			})
			this.getList()
		},
		methods: {
			getList() {
				let listArr = []
				this.covers.map(item => {
					const obj = {
						id: item.id,
						width:item.width,
						height:item.height,
						//将经纬度进行重新赋值，不覆盖将默认latitude: 39.909,longitude: 116.39742,时地图移动到标点位置
						latitude: this.latitude = item.latitude,
						longitude: this.longitude = item.longitude,
						iconPath: item.url //给每一个坐标添加图片
					}
					listArr.push(obj)

				})
				this.covers = listArr
			},
			onMarkerTap(e) {
			      const { id } = this.covers.find(covers => covers.id === e.markerId);
			      // 处理点击事件，如弹窗展示信息等
				  // 点击事件中处理页面跳转
				  this.$Router.push({
				  	name: 'vrBrowsing',
				  	params: {
				  		orderId:id,
				  	}
				  })
			    }
		}
	}
</script>
<style>
	.u-navbar__content{
		background-color:rgba(255, 255, 255, 0) !important;
	}
</style>
<style lang="scss">
  .paers-max{
	  min-width:100vw;
	  height:100vh;
  }
  .paers-Navbar{
	  width:100%;
	  position: fixed;
	  top: 0;
	  z-index: 9999999;
	  
  }
  .paers-background{
	  width:100%;
	  height:330rpx;
	  position: fixed;
	  top: 0;
	  left: 0;
	  right: 0;
	  z-index: 9999;
  }
</style>