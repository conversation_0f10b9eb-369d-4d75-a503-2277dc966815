<template>
	<view>
		
		<u-navbar :title="title" :bgColor='bgColor'
			:titleStyle="titleStyle + 'padding-left:80rpx;flex:1; font-weight: 400;font-size: 30rpx;text-align: left; color:#3B65A5;'"
			:leftIconColor='leftIconColor' :placeholder="placeholder" :fixed="fixed" :safeAreaInsetTop="safeAreaInsetTop"
			:autoBack="false" :leftIconSize="!isShowLeft ? '0' : '20px'" @leftClick="onLeftClick">
			<view class="u-nav-slot" slot="left">
				<image class="nav-left-icon" :src="imageUrl + '/nav-left-icon.png'"></image>
			</view>
		</u-navbar>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String
			},
			isShowLeft: {
				type: Boolean,
				default: true
			},
			bgColor: {
				type: String,
				default: '#E8F1FE'
			},
			titleStyle: {
				type: String,
				default: ''
			},
			leftIconColor: {
				type: String,
				default: '#000'
			},
			placeholder: {
				type: Boolean,
				default: true
			},
			fixed: {
				type: Boolean,
				default: true
			},
			safeAreaInsetTop: {
				type: Boolean,
				default: true
			},
			isLeftClick: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				imageUrl: this.$imageUrl
			}
		},
		methods: {
			onLeftClick() {
				if (getCurrentPages().length > 1) {
					this.$Router.back()
				} else {
					uni.reLaunch({
						url: '/pages/home'
					});
				}
			}
		}
	}
</script>

<style lang="scss">
	.nav-left-icon {
		width: 36rpx;
		height: 36rpx;
	}

	.nav-center-title {
		font-size: 30rpx;
		color: #3B65A5;
	}

	/* #ifndef APP-NVUE */
	page {
		background-color: $u-bg-color;
	}

	/* #endif */

	.u-page {
		padding: 0;
		flex: 1;
		background-color: $u-bg-color;

		&__item {

			&__title {
				color: $u-tips-color;
				background-color: $u-bg-color;
				padding: 15px;
				font-size: 15px;

				&__slot-title {
					color: $u-primary;
					font-size: 14px;
				}
			}
		}
	}

	.u-nav-slot {
		@include flex;
		align-items: center;
		justify-content: space-between;
	}
</style>