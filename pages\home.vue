<template>
	<view class="home-view">
		<swiper class="swiper" vertical>
			<swiper-item class="swiper-item">
				<image class="top-bg" :src="imageUrl + '/home/<USER>'" mode="heightFix"></image>
				<view class="item-box">
					<view class="swiper-windows" @click="goToUrl('personalizedMap')">
						<image class="windows-image-1" :style="status == 1 ? 'opacity: 1;' : 'opacity: 0;'"
							:src="imageUrl + '/home/<USER>'">
						</image>
						<image class="windows-image-2" :style="status == 2 ? 'opacity: 1;' : 'opacity: 0;'"
							:src="imageUrl + '/home/<USER>'">
						</image>
						<image class="windows-image-3" :style="status == 3 ? 'opacity: 1;' : 'opacity: 0;'"
							:src="imageUrl + '/home/<USER>'">
						</image>
						<image class="windows-image-4" :style="status == 4 ? 'opacity: 1;' : 'opacity: 0;'"
							:src="imageUrl + '/home/<USER>'">
						</image>
						<image class="windows-image-5" :style="status == 5 ? 'opacity: 1;' : 'opacity: 0;'"
							:src="imageUrl + '/home/<USER>'">
						</image>
						<image class="windows-mask" :src="imageUrl + '/home/<USER>'"></image>
						<image class="windows-bg" :src="imageUrl + '/home/<USER>'"></image>
					</view>
				</view>
				<view class="title-image">
					<image :src="imageUrl + '/home/<USER>'" mode="widthFix"></image>
				</view>
				<view class="tips-box flex-box-space-between" @click="goToUrl('voteView')">
					<image :src="imageUrl + '/home/<USER>'"></image>
					<view>2024长沙学院第三届‘月湖杯’网络投票开始啦~</view>
				</view>
				<view class="vote-btn" @click="goToUrl('voteView')">
					<image :src="imageUrl + '/home/<USER>'" mode="widthFix"></image>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	import {
		wxLogin
	} from "@/api/public/index.js"
	export default {
		data() {
			return {
				imageUrl: this.$imageUrl,
				isShow: true,
				status: 1,
				times: null,
				isVote: ''
			};
		},

		onLoad() {
			this.isVote = this.$Route.query.isVote
			this.getCode()
			this.times = setInterval(() => {
				this.setWindowsImage()
			}, 3000)
		},
		onUnload() {
			this.times = null
			clearInterval(this.times)
		},
		methods: {
			// 获取code登录
			getCode() {
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				uni.login({
					provider: 'weixin',
					success: res => {
						this.code = res.code
						this.wxLogin()
					},
					fail: err => {
						uni.hideLoading()
						console.log(err)
					}
				})
			},
			// 登录请求
			async wxLogin() {
				let res = await wxLogin(this.code, this.$appID)
				uni.setStorageSync('token', res.data.token);
				uni.setStorageSync('wxUser', res.data.wxUser);
				uni.setStorageSync('openId', res.data.openId);
				uni.hideLoading()
				if (this.isVote == 'vote') {
					this.goToUrl('voteView')
				}
			},
			setWindowsImage() {
				if (this.status == 5) {
					this.status = 1
				} else {
					this.status++
				}
			},
			goToUrl(urlName, params = {}) {
				this.$Router.push({
					name: urlName,
					params
				})
			}
		}
	}
</script>

<style lang="scss">
	.home-view {
		width: 100vw;
		height: 100vh;
	}

	.swiper {
		width: 100vw;
		height: 100vh;
	}

	.swiper-item {
		position: relative;
		width: 100%;
		height: 100%;

		.item-box {
			position: relative;
			width: 100%;
			height: 100%;
			background: url("https://xnlv.lzxx8848.com/image/xnlv/static/home/<USER>") no-repeat;
			background-size: 100% 100%;

			.swiper-windows {
				height: 546rpx;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				width: 100%;

				.windows-image-1,
				.windows-image-2,
				.windows-image-3,
				.windows-image-4,
				.windows-image-5 {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 520rpx;
					height: 520rpx;
					opacity: 1;
					transition: all 3s;
				}

				.windows-image-1 {
					z-index: 5;
				}

				.windows-image-2 {
					z-index: 4;
				}

				.windows-image-3 {
					z-index: 3;
				}

				.windows-image-4 {
					z-index: 2;
				}

				.windows-image-5 {
					z-index: 1;
				}

				.windows-bg,
				.windows-mask {
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					width: 546rpx;
					height: 546rpx;
					z-index: 99;
				}
			}
		}
	}

	.top-bg {
		position: absolute;
		top: 8%;
		left: 0;
		height: 358rpx;
		z-index: 9;
	}

	.title-image {
		position: absolute;
		bottom: 25%;
		left: 50%;
		transform: translateX(-50%);

		image {
			width: 432rpx;
		}
	}

	.tips-box {
		position: absolute;
		bottom: 15%;
		left: 50%;
		transform: translateX(-50%);
		width: 702rpx;
		height: 82rpx;
		background: rgba(255, 255, 255, 0.6);
		border-radius: 12rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		font-size: 26rpx;
		color: #000000;
		font-weight: bold;

		image {
			width: 54rpx;
			height: 54rpx;
		}
	}

	.vote-btn {
		position: absolute;
		bottom: 5%;
		left: 50%;
		transform: translateX(-50%);

		image {
			width: 358rpx;
		}
	}
</style>