import Api from "@/api/index.js"

// 投票活动首页
export function voteIndex(data) {
	return Api.getDataHttp('/vtp/app/voteActivity/index', data)
}
// 作品分页列表
export function getWorkList(data) {
	return Api.getDataHttp('/vtp/app/voteActivity/getWorkList', data)
}
// 作品排名分页列表
export function getWorkRankList(data) {
	return Api.getDataHttp('/vtp/app/voteActivity/getWorkRankList', data)
}
// 微信用户投票
export function voteSubmit(id) {
	return Api.postIdHttp('/vtp/app/voteActivity/submit/', id)
}