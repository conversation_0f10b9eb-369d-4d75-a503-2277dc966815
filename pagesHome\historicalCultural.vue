<template>
	<!-- 点击俯视按钮进来的vr浏览 -->
	<view class="hidor-max">
		<web-view  :src="dominate"  subcontext="true" style="width:100%;height:100%;" ></web-view>
		<cover-view class="coverTap">
			<cover-view class="vr-browse-bottom">
				<cover-view style="width:120rpx;;" class="flex-box-center">
					<cover-image @click="copperOfficialTap()" :src="imageUrl + '/vrTravel/historicalCulturals.png'"
						mode="widthFix" style="width:110rpx;height:95rpx;"></cover-image>
				</cover-view>
				<cover-view style="width:120rpx;" class="flex-box-center">
					<cover-image  :src="imageUrl + '/vrTravel/topView.png'" mode="widthFix"
						style="width:88rpx;height:95rpx;"></cover-image>
				</cover-view>
				<cover-view style="width:120rpx;" class="flex-box-center">
					<button open-type="share" style="z-index:999999;background-color:#FFFFFF00;">
						<cover-image :src="imageUrl + '/vrTravel/share.png'" mode="widthFix"
							style="width:60rpx;height:95rpx;"></cover-image>
					</button>
				</cover-view>
			</cover-view>
		</cover-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imageUrl: this.$imageUrl,
				orderIdTap:'',
				dominate:'',
			};
		},
		onLoad() {
			this.$AppReady.then(() => {
				// vr浏览
				this.orderIdTap = this.$Route.query.orderIdTap
				this.overlookTap()
			})
		},
		methods:{
			// 俯视vr
			overlookTap(){
				// 乔口古镇
				if (this.orderIdTap == '0') {
					this.dominate = 'https://www.720yun.com/vr/bdajzOufky2?s=15321238'
				}
				// <!-- 靖港 -->
				if (this.orderIdTap == '1') {
					this.dominate = 'https://www.720yun.com/vr/c7f25qbv9cs?s=2298663'
				}
				// /新康
				if (this.orderIdTap == '2') {
					this.dominate = 'https://www.720yun.com/vr/a8fjzOuf5m4?s=15322097'
				}
				// /铜官
				if (this.orderIdTap == '3') {
					this.dominate = 'https://www.720yun.com/vr/d19jzOuf5f8?s=15322040'
				}
				// 书堂山
				if (this.orderIdTap == '4') {
					this.dominate = 'https://www.720yun.com/vr/4f5jzOuf5O1?s=15322235'
				}
			},
			// 点击跳转铜管浏览
			copperOfficialTap() {
				this.$Router.push({
					name: 'culturalCenter',
					params: {
						orderIdTap: this.orderIdTap,
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.hidor-max {
		width: 100vw;
		height: 100vh;
	}
	.coverTap {
		position: fixed;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.21);
		height: 143rpx;
		z-index: 100;
		z-index: 99999;
		bottom: 0;
	}
	
	.vr-browse-bottom {
		width: 100%;
		margin-top: 16rpx;
		display: flex;
		justify-content: space-around;
	}
	
	button::after {
		border: none;
		background: none !important;
	}
</style>
