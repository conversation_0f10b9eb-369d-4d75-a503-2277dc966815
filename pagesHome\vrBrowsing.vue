<template>
	<!-- 点击地图进来的vr浏览 -->
	<view class="vr-max">
		<!-- <u-transition :show="show" :duration="5500" mode="fade"> -->
		<!-- // 720yun VR浏览 -->
		<view class="vr-web">
			<!-- vr浏览 -->
			<web-view  subcontext="true" :src="video" style="" >
			</web-view>
			<cover-view class="coverTap">
				<cover-view class="vr-browse-bottom">
					<cover-view style="width:120rpx;;" class="flex-box-center">
						<cover-image @click="copperOfficialTap()" :src="imageUrl + '/vrTravel/historicalCulturals.png'"
							mode="widthFix" style="width:110rpx;height:95rpx;"></cover-image>
					</cover-view>
					<cover-view style="width:120rpx;" class="flex-box-center">
						<cover-image @click="overlook()" :src="imageUrl + '/vrTravel/topView.png'" mode="widthFix"
							style="width:88rpx;height:95rpx;"></cover-image>
					</cover-view>
					<cover-view style="width:120rpx;" class="flex-box-center">
						<button open-type="share" style="z-index:999999;background-color:#FFFFFF00;">
							<cover-image :src="imageUrl + '/vrTravel/share.png'" mode="widthFix"
								style="width:60rpx;height:95rpx;"></cover-image>
						</button>
					</cover-view>
				</cover-view>
			</cover-view>
		</view>
		<!-- </u-transition> -->
	</view>

</template>

<script>
	export default {
		data() {
			return {
				imageUrl: this.$imageUrl,
				orderId: '',
				video: '',
				show: true,
			};
		},
		onShow() {

		},
		onLoad() {
			// this.$AppReady.then(() => {
			this.orderId = this.$Route.query.orderId
			this.switchTap()
			// })
		},
		onShareAppMessage(e) {
			return {
				title: '湘江古镇云游',
				path: '/pages/home',
				imageUrl: 'https://xnlv.lizxx.com/image/xnlv/static/vrTravel/coverTap.png',
			};
		},
		methods: {
			switchTap() {
				// vr浏览
					// 乔口古镇
					if (this.orderId == '0') {
						this.video = 'https://www.720yun.com/vr/bdajzOufky2'
					}
					// <!-- 靖港 -->
					if (this.orderId == '1') {
						this.video = 'https://www.720yun.com/vr/c7f25qbv9cs'
					}
					// /新康
					if (this.orderId == '2') {
						this.video = 'https://www.720yun.com/vr/a8fjzOuf5m4'
					}
					// /铜官
					if (this.orderId == '3') {
						this.video = 'https://www.720yun.com/vr/d19jzOuf5f8'
					}
					// 书堂山
					if (this.orderId == '4') {
						this.video = 'https://www.720yun.com/vr/4f5jzOuf5O1'
				}
			},
			// 点击跳转铜管浏览
			copperOfficialTap() {
				this.$Router.push({
					name: 'culturalCenter',
					params: {
						orderIdTap: this.orderId,
					}
				})
			},
			// 点击查看俯视VR
			overlook() {
				this.$Router.push({
					name: 'historicalCultural',
					params: {
						orderIdTap: this.orderId,
					}
				})
			}
		}
	}
</script>
<style>

</style>
<style lang="scss">
	.vr-max {
		width: 100vw;
		height: 100vh;
	}

	.vr-web {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.coverTap {
		position: fixed;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.21);
		height: 143rpx;
		z-index: 100;
		z-index: 99999;
		bottom: 0;
	}

	.vr-browse-bottom {
		width: 100%;
		margin-top: 16rpx;
		display: flex;
		justify-content: space-around;
	}

	button::after {
		border: none;
		background: none !important;
	}
</style>