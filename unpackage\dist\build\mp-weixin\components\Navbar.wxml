<view><u-navbar vue-id="6b53353b-1" title="{{title}}" bgColor="{{bgColor}}" titleStyle="{{titleStyle+'padding-left:80rpx;flex:1; font-weight: 400;font-size: 30rpx;text-align: left; color:#3B65A5;'}}" leftIconColor="{{leftIconColor}}" placeholder="{{placeholder}}" fixed="{{fixed}}" safeAreaInsetTop="{{safeAreaInsetTop}}" autoBack="{{false}}" leftIconSize="{{!isShowLeft?'0':'20px'}}" data-event-opts="{{[['^leftClick',[['onLeftClick']]]]}}" bind:leftClick="__e" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot" slot="left"><image class="nav-left-icon" src="{{imageUrl+'/nav-left-icon.png'}}"></image></view></u-navbar></view>