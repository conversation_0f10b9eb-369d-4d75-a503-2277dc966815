<view class="paers-max"><view class="paers-Navbar"><navbar vue-id="4c4f0278-1" leftIconColor="#0000000" bgColor="#0000000" bind:__l="__l"></navbar></view><view class="paers-background"><image style="width:100%;height:100%;" src="{{imageUrl+'/vrTravel/blueSky.png'}}" mode="widthFix"></image></view><view><map style="width:100%;height:100vh;" id="map" subkey="N3ZBZ-FI73T-PUXXN-V6JUL-UKQS5-HLBTR" longitude="112.79028" latitude="28.45748" scale="12" layer-style="1" markers="{{covers}}" data-event-opts="{{[['markertap',[['onMarkerTap',['$event']]]]]}}" bindmarkertap="__e"></map></view></view>