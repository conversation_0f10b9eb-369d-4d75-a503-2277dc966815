{"name": "uni-simple-router", "version": "2.0.8-beta.4", "description": "> 一个更为简洁的[Vue-router](https://router.vuejs.org/zh/)，专为 [uni-app](https://uniapp.dcloud.io/) 量身打造", "main": "dist/uni-simple-router.js", "types": "dist/uni-simple-router.d.ts", "scripts": {"dev": "webpack --watch  --progress --config webpack/webpack.dev.js", "dist": "webpack --progress --config webpack/webpack.prod.js", "dist:dts": "api-extractor run --local --verbose", "lint": "eslint --ext .js,.ts src", "lintFix": "eslint --ext .js,.ts src --fix", "test": "jest test/query-toggle.spec.ts", "publish": "node ./publish/index.js", "build": "node ./publish/build.js"}, "repository": {"type": "git", "url": "git+https://github.com/SilurianYang/uni-simple-router.git"}, "keywords": ["router", "uni-app-router", "interceptor", "uni-app", "uniapp"], "author": "h<PERSON>ng", "license": "MIT", "bugs": {"url": "https://github.com/SilurianYang/uni-simple-router/issues"}, "homepage": "https://github.com/SilurianYang/uni-simple-router#readme", "__npminstall_done": true, "_from": "uni-simple-router@2.0.8-beta.4", "_resolved": "https://registry.npmmirror.com/uni-simple-router/-/uni-simple-router-2.0.8-beta.4.tgz"}