{"name": "uni-read-pages", "version": "1.0.5", "description": "read `pages.json` file to generate the routes table", "main": "index.js", "directories": {"example": "examples"}, "scripts": {"postinstall": "node -e \"console.log('\\x1b[91m','\\n\\n uni-simple-router 垫脚片，欢迎下载！\\n \\n 开源不易，需要鼓励。去给 uni-read-pages 项目 点个 star 吧 \\n\\n')\"", "dev": "webpack --watch  --progress --config webpack/webpack.dev.js", "build": "webpack --progress --config webpack/webpack.prod.js"}, "repository": {"type": "git", "url": "git+https://github.com/SilurianYang/uni-read-pages.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/SilurianYang/uni-read-pages/issues"}, "homepage": "https://github.com/SilurianYang/uni-read-pages#readme", "__npminstall_done": true, "_from": "uni-read-pages@1.0.5", "_resolved": "https://registry.npmmirror.com/uni-read-pages/-/uni-read-pages-1.0.5.tgz"}