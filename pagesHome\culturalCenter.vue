<template>
	<!-- 点击历史文化馆按钮进来的vr浏览 -->
	<view class="cult-max">
		<!-- vr浏览 -->
		<web-view  :src="videoTap" subcontext="true" style="width:100%;height:100%;"></web-view>
		<cover-view class="coverTap">
			<cover-view class="vr-browse-bottom">
				<cover-view style="width:120rpx;;" class="flex-box-center">
					<cover-image  :src="imageUrl + '/vrTravel/historicalCulturals.png'"
						mode="widthFix" style="width:110rpx;height:95rpx;"></cover-image>
				</cover-view>
				<cover-view style="width:120rpx;" class="flex-box-center">
					<cover-image @click="overlook()" :src="imageUrl + '/vrTravel/topView.png'" mode="widthFix"
						style="width:88rpx;height:95rpx;"></cover-image>
				</cover-view>
				<cover-view style="width:120rpx;" class="flex-box-center">
					<button open-type="share" style="z-index:999999;background-color:#FFFFFF00;">
						<cover-image :src="imageUrl + '/vrTravel/share.png'" mode="widthFix"
							style="width:60rpx;height:95rpx;"></cover-image>
					</button>
				</cover-view>
			</cover-view>
		</cover-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imageUrl: this.$imageUrl,
				videoTap:'',
				orderIdTap:'',
			};
		},
		onLoad() {
			this.$AppReady.then(() => {
				// vr浏览
				this.orderIdTap = this.$Route.query.orderIdTap
				this.cultureTap()
			})
		},
		methods:{
			// vr浏览
			cultureTap() {
				// 乔口古镇 
				if (this.orderIdTap == '0') {
					this.videoTap = 'https://roma.720yun.com/vr/ba67ce6772077604'
				}
				// <!-- 靖港 -->
				if (this.orderIdTap == '1') {
					this.videoTap = 'https://roma.720yun.com/vr/503b2cafc03e82fa'
				}
				// /新康
				if (this.orderIdTap == '2') {
					this.videoTap = 'https://roma.720yun.com/vr/df4fb06461a32dc0'
				}
				// /铜官
				if (this.orderIdTap == '3') {
					this.videoTap = 'https://roma.720yun.com/vr/c0e1288d4ee2a39f'
				}
				// 书堂山
				if (this.orderIdTap == '4') {
					this.videoTap = 'https://roma.720yun.com/vr/8fd6ee40c350b4d8'
				}
			},
			// 点击查看俯视VR
			overlook() {
				this.$Router.push({
					name: 'historicalCultural',
					params: {
						orderIdTap: this.orderIdTap,
					}
				})
			}
		}
	}
</script>

<style lang="scss">
  .cult-max{
	  width: 100vw;
	  height: 100vh;
  }
  .coverTap {
  	position: fixed;
  	width: 100%;
  	background-color: rgba(0, 0, 0, 0.21);
  	height: 143rpx;
  	z-index: 100;
  	z-index: 99999;
  	bottom: 0;
  }
  
  .vr-browse-bottom {
  	width: 100%;
  	margin-top: 16rpx;
  	display: flex;
  	justify-content: space-around;
  }
  
  button::after {
  	border: none;
  	background: none !important;
  }
</style>
