<view class="vote-situation-view"><navbar vue-id="3abf597c-1" title="投票情况" leftIconColor="#3b65a5" bind:__l="__l"></navbar><view class="situation-box"><view class="situation-title">投票情况</view><z-paging class="situation-list-box vue-ref" vue-id="3abf597c-2" fixed="{{false}}" data-ref="paging" value="{{dataList}}" data-event-opts="{{[['^query',[['queryList']]],['^input',[['__set_model',['','dataList','$event',[]]]]]]}}" bind:query="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default','top']}}"><view class="situation-nav" slot="top"><u-tabs vue-id="{{('3abf597c-3')+','+('3abf597c-2')}}" list="{{navList}}" current="{{tabsIndex}}" itemStyle="height: 44px; flex: 1;" lineWidth="100rpx" lineColor="#3A64A5" activeStyle="color: #3A64A5; font-size: 30rpx; font-weight: bold" inactiveStyle="font-size: 30rpx; color: #333333;" data-event-opts="{{[['^click',[['onTabs']]]]}}" bind:click="__e" bind:__l="__l"></u-tabs></view><view class="situation-list"><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="situation-item"><view class="situation-item-title flex-box-space-between">{{''+(item.workName||'')+''}}<view style="color:#999999;">{{''+(item.totalVoteNum||0)+'票'}}</view></view><view class="situation-group">{{'参赛队伍：'+(item.partTeam||'')+''}}</view></view></block></view></z-paging></view></view>