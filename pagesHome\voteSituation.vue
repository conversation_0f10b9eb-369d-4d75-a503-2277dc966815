<template>
	<view class="vote-situation-view">
		<Navbar title="投票情况" :leftIconColor="'#3b65a5'" />
		<view class="situation-box">
			<view class="situation-title">
				投票情况
			</view>
			<z-paging class="situation-list-box" :fixed="false" ref="paging" v-model="dataList" @query="queryList">
				<view class="situation-nav" slot="top">
					<u-tabs :list="navList" :current="tabsIndex" itemStyle="height: 44px; flex: 1;" lineWidth="100rpx"
						lineColor="#3A64A5" activeStyle="color: #3A64A5; font-size: 32rpx; font-weight: bold"
						inactiveStyle="font-size:32rpx; color: #333333;" @click="onTabs"></u-tabs>
				</view>
				<view class="situation-list">
					<view class="situation-item" v-for="(item, index) in dataList" :key="index">
						<view class="situation-item-title flex-box-space-between">
							{{item.workName || ''}}
							<view style="color:#999999;">
								{{item.totalVoteNum || 0}}票
							</view>
						</view>
						<view class="situation-group">
							参赛队伍：{{item.partTeam || ''}}
						</view>
					</view>
				</view>
			</z-paging>
		</view>
	</view>
</template>

<script>
	import {
		getWorkRankList
	} from "@/api/vote/vote.js"
	export default {
		data() {
			return {
				dataList: [],
				tabsIndex: 0,
				type: 'szgy',
				navList: [{
					name: '数字工业',
					type: 'szgy'
				}, {
					name: '数字人居',
					type: 'szrj'
				}, {
					name: '数字文化',
					type: 'szwh'
				}, {
					name: '元宇宙',
					type: 'yyz'
				}],
			}
		},
		onLoad() {
			this.tabsIndex = +this.$Route.query.tabsIndex
			this.type = this.navList[this.tabsIndex || 0].type
			console.log(this.tabsIndex)
		},
		onShareAppMessage() {
			return {
				title: '2024长沙学院第三届‘月湖杯’网络投票开始啦~',
				path: '/pages/home?isVote=vote',
				imageUrl: 'https://xnlv.lzxx8848.com/image/xnlv/static/shear-image.png'
			}
		},
		onShareTimeline() {
			return {
				title: '2024长沙学院第三届‘月湖杯’网络投票开始啦~',
			};
		},
		methods: {
			queryList(pageNo, pageSize) {
				getWorkRankList({
					pageSize: pageSize,
					pageNum: pageNo,
					workClassify: this.type
				}).then((res) => {
					this.$refs.paging.complete(res.rows)
				})
			},
			onTabs(e) {
				if (this.tabsIndex == e.index) return
				this.tabsIndex = e.index
				this.type = e.type
				this.pageNum = 1
				this.$refs.paging.reload()
			},
			goToUrl(urlName, params = {}) {
				this.$Router.push({
					name: urlName,
					params
				})
			}
		}
	}
</script>

<style lang="scss">
	::v-deep .u-tabs__wrapper__nav__item {
		flex: 1 !important;
	}

	::v-deep .z-paging-content {
		flex: 1;
	}

	.vote-situation-view {
		display: flex;
		flex-direction: column;
		background-color: #e8f1fe;
		min-height: 100vh;
	}

	.situation-box {
		display: flex;
		flex-direction: column;
		height: 88vh;
		margin: 16rpx 18rpx 40rpx;
		background: #fff;
		border-radius: 12rpx;
		box-sizing: border-box;
	}

	.situation-title {
		font-weight: bold;
		font-size: 32rpx;
		color: #3A64A5;
		text-align: center;
		margin-top: 38rpx;
	}

	.situation-nav {
		margin-top: 16rpx;
		padding-bottom: 20rpx;
	}

	.situation-list-box {
		padding: 0 0 20rpx;
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.situation-list {
		padding: 10rpx 28rpx;
		flex: 1;
	}

	.situation-item {
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #EFEFEF;
		.situation-item-title {
			font-size:30rpx;
			color: #333333;
			font-weight: bold;
		}

		.situation-group {
			font-size: 24rpx;
			color: #666666;
			margin-top:20rpx;
		}
	}

	::v-deep .zp-l-text-rpx {
		font-size: 26rpx !important;
	}
</style>